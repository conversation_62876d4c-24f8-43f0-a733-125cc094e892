/**
 * CourseAssignment
 * Displays all tech roadmap assignments for super admins
 */
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { Card, Table, Button, message, Tooltip, Modal, Input } from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FileOutlined,
  UploadOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
  ExclamationCircleFilled,
} from '@ant-design/icons';
import request from 'utils/request';
import { ROLES } from 'containers/constants';
import moment from 'moment';
// import TechRoadmapMentee from 'components/TechRoadmapMentee';
import TechRoadmap from 'containers/TechRoadmap';
import { API_ENDPOINTS } from './constants';
import MenteeRoadmapForm from '../MenteeTechRoadmap/MenteeRoadmapForm';
import { isSuperAdmin } from '../../components/SideBar';
import { getUserData } from '../../utils/Helper';

// const { Option } = Select;

const CourseAssignment = () => {
  const [loading, setLoading] = useState(false);
  const [assignments, setAssignments] = useState([]);
  const [filteredAssignments, setFilteredAssignments] = useState([]);
  // const [isModalVisible, setIsModalVisible] = useState(false);
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false);
  const [
    menteeRoadmapFormModalVisible,
    setMenteeRoadmapFormModalVisible,
  ] = useState(false);
  const [selectedMentee] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [urlMenteeId, setUrlMenteeId] = useState(null);
  const [employeeData, setEmployeeData] = useState(null);
  const [uploadLoading, setUploadLoading] = useState({});
  const [documentPreviewModal, setDocumentPreviewModal] = useState({
    visible: false,
    url: '',
    title: '',
    documentId: '',
    assignmentId: '',
    approvalStatus: 'pending',
  });

  const [commentsModal, setCommentsModal] = useState({
    visible: false,
    assignmentId: '',
    mentorComments: '',
    menteeComments: '',
    commentType: 'mentor', // 'mentor' or 'mentee'
    title: '',
  });

  const [approvalLoading, setApprovalLoading] = useState(false);
  const [commentLoading, setCommentLoading] = useState(false);
  const [isUpdatingPercentage, setIsUpdatingPercentage] = useState(false);

  const user = getUserData();
  const isSuperAdminUser = isSuperAdmin(user);
  // console.log('This is a superadmin user or not', isSuperAdminUser);
  // Extract menteeId from URL if present
  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const menteeId = queryParams.get('menteeId');
    const employeeId = queryParams.get('employeeId');

    if (menteeId) {
      setUrlMenteeId(menteeId);
    }

    if (employeeId) {
      fetchEmployeeProfile(employeeId);
    }
  }, []);

  const fetchCurrentUser = async () => {
    try {
      const response = await request(API_ENDPOINTS.USER_DETAILS_API, {
        method: 'GET',
      });
      if (response && response.data) {
        setCurrentUser(response.data);
      }
    } catch (error) {
      message.error('Failed to fetch user details');
    }
  };

  const handleDelete = record => {
    Modal.confirm({
      title: 'Are you sure you want to delete this course assignment?',
      content: 'This action cannot be undone.',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: async () => {
        try {
          setLoading(true);
          const response = await request(
            `${API_ENDPOINTS.DELETE_COURSE_ASSIGNMENT}/${record._id}`,
            {
              method: 'DELETE',
            },
          );
          if (response.status === 200) {
            message.success('Course assignment deleted successfully');
            // Refresh the assignments list
            await fetchAllAssignments();
          }
        } catch (error) {
          message.error(error.message || 'Error deleting course assignment');
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const fetchEmployeeProfile = async employeeId => {
    try {
      const requestURL = `${API_ENDPOINTS.EMPLOYEE_PROFILE}/${employeeId}`;

      const response = await request(requestURL, {
        method: 'GET',
      });
      if (response && response.data) {
        setEmployeeData(response.data);
        // We're removing the fetchCoursesByRole call since it's not implemented yet
      } else {
        // Response successful but no data found
      }
    } catch (error) {
      message.error('Failed to fetch employee profile data');
    }
  };

  // const fetchMentees = async () => {
  //   try {
  //     const response = await request(API_ENDPOINTS.GET_MENTEE, {
  //       method: 'GET',
  //     });
  //     if (response && response.data) {
  //       setMentees(response.data);
  //     }
  //   } catch (error) {
  //     console.warn('Could not fetch mentees:', error);
  //     // Don't show error message to user as this is not critical
  //     setMentees([]);
  //   }
  // };

  // // Function to get user details by ID
  // const getUserDetailsById = async userId => {
  //   try {
  //     const response = await request(`${API_ENDPOINTS.USERS}/${userId}`, {
  //       method: 'GET',
  //     });
  //     return response && response.data ? response.data : null;
  //   } catch (error) {
  //     console.error(`Error fetching user ${userId} details:`, error);
  //     return null;
  //   }
  // };

  const fetchAllAssignments = async () => {
    try {
      setLoading(true);

      // Use the dedicated endpoint for fetching all techroadmap assignments
      const response = await request(
        API_ENDPOINTS.TECH_ROADMAP_ALL_ASSIGNMENTS,
        {
          method: 'GET',
        },
      );

      // Check if we have a valid response with data
      if (response && response.data) {
        if (response.data.length > 0) {
          // Process the assignments data - now the backend already includes user details
          const processedAssignments = response.data.map(assignment => {
            // Use the completionPercentage from the database if available, otherwise fallback to calculating based on status
            let { completionPercentage } = assignment;

            // If completionPercentage is not available from the database, calculate it based on status
            if (
              completionPercentage === undefined ||
              completionPercentage === null
            ) {
              if (
                assignment.completionStatus === 'Completed' ||
                assignment.completionStatus === 'Rated'
              ) {
                completionPercentage = 100;
              } else if (assignment.completionStatus === 'Assigned') {
                // Default value for in-progress assignments
                completionPercentage = 78;
              } else {
                completionPercentage = 0;
              }
            }

            return {
              ...assignment,
              // Use the names provided by the backend, or fallback to placeholders
              menteeName: assignment.menteeName || 'Unknown Mentee',
              mentorName: assignment.mentorName || 'Unknown Mentor',
              employeeId: assignment.employeeId || 'N/A',
              completionPercentage,
            };
          });

          // Apply filters based on URL menteeId and user role
          let assignmentsToFilter = [...processedAssignments];

          // If a specific mentee ID is provided in the URL, filter by that mentee
          if (urlMenteeId) {
            assignmentsToFilter = assignmentsToFilter.filter(
              assignment => assignment.menteeId === urlMenteeId,
            );
          }
          // If user is not HR or ADMIN, filter assignments to only show those where they are the mentor
          else if (
            currentUser &&
            currentUser.role !== ROLES.HR &&
            currentUser.role !== ROLES.ADMIN
          ) {
            assignmentsToFilter = assignmentsToFilter.filter(
              assignment => assignment.mentorId === currentUser._id,
            );
          }

          // Store filtered assignments in state
          setFilteredAssignments(assignmentsToFilter);
          // Store all assignments in state (for reference)
          setAssignments(processedAssignments);

          return assignmentsToFilter; // Return the filtered assignments for direct use
        }
        message.info('No course assignments found');
        setFilteredAssignments([]);
        setAssignments([]);
        return [];
      }
      message.error('Failed to fetch assignments');
      setFilteredAssignments([]);
      setAssignments([]);
      return [];
    } catch (error) {
      message.error('Failed to fetch assignments');
      setFilteredAssignments([]);
      setAssignments([]);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Function to filter assignments based on current user and URL parameters
  const filterAssignments = () => {
    if (!assignments.length) return [];

    // Apply filters based on URL menteeId and user role
    const assignmentsToFilter = [...assignments];

    // If a specific mentee ID is provided in the URL, filter by that mentee
    if (urlMenteeId) {
      return assignmentsToFilter.filter(
        assignment => assignment.menteeId === urlMenteeId,
      );
    }

    // If user is HR or ADMIN, show all assignments
    if (
      currentUser &&
      (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)
    ) {
      return assignmentsToFilter;
    }

    // For any other case (regular user accessing from elsewhere), show only their own assignments
    if (currentUser) {
      return assignmentsToFilter.filter(
        assignment => assignment.menteeId === currentUser._id,
      );
    }

    return assignmentsToFilter;
  };

  useEffect(() => {
    // First fetch the current user and course data
    const init = async () => {
      await fetchCurrentUser();
      await fetchAllAssignments();
      // Don't try to fetch mentees if the endpoint doesn't exist
      // fetchMentees();
    };

    init();
  }, []);

  // const handleAssignCourse = mentee => {
  //   setSelectedMentee(mentee);
  //   setIsModalVisible(true);
  // };

  // const handleModalCancel = () => {
  //   setIsModalVisible(false);
  //   setSelectedMentee(null);
  // };

  const handleCourseModalCancel = () => {
    setIsCourseModalVisible(false);
  };

  const handleMenteeRoadmapFormModalCancel = () => {
    setMenteeRoadmapFormModalVisible(false);
  };

  const handleCourseModalSuccess = () => {
    setIsCourseModalVisible(false);
    // Refresh assignments if needed
    fetchAllAssignments();
  };

  // const handleDelete = async record => {
  //   try {
  //     setLoading(true);
  //     const response = await request(
  //       `${API_ENDPOINTS.TECH_ROADMAP_MENTEE}/${record._id}`,
  //       {
  //         method: 'DELETE',
  //       },
  //     );

  //     if (response && response.status === 200) {
  //       message.success('Assignment deleted successfully');
  //       fetchAllAssignments();
  //     }
  //   } catch (error) {
  //     message.error('Failed to delete assignment');
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // const getStatusTag = status => {
  //   switch (status) {
  //     case 'Completed':
  //       return (
  //         <Tag color="green" icon={<CheckCircleOutlined />}>
  //           Completed
  //         </Tag>
  //       );
  //     case 'Rated':
  //       return (
  //         <Tag color="blue" icon={<StarOutlined />}>
  //           Rated
  //         </Tag>
  //       );
  //     default:
  //       return (
  //         <Tag color="orange" icon={<ClockCircleOutlined />}>
  //           Assigned
  //         </Tag>
  //       );
  //   }
  // };

  // Apply filtering whenever currentUser or assignments change
  useEffect(() => {
    if (currentUser) {
      const filtered = filterAssignments();
      setFilteredAssignments(filtered);
    }
  }, [currentUser, assignments, urlMenteeId]);

  // Apply search text filter on top of role-based filtering
  const getSearchFilteredAssignments = () =>
    filteredAssignments.filter(item => {
      // Apply search text filter if present
      if (searchText) {
        return (
          (item.menteeName &&
            item.menteeName.toLowerCase().includes(searchText.toLowerCase())) ||
          (item.courseName &&
            item.courseName.toLowerCase().includes(searchText.toLowerCase())) ||
          (item.mentorName &&
            item.mentorName.toLowerCase().includes(searchText.toLowerCase()))
        );
      }
      return true;
    });

  // Define columns based on user role
  const getColumns = () => {
    // Base columns that everyone sees
    const baseColumns = [
      {
        title: 'Course name',
        dataIndex: 'courseName',
        key: 'courseName',
        render: text => <span>{text || 'N/A'}</span>,
      },
      {
        title: 'Course duration',
        dataIndex: 'duration',
        key: 'duration',
        render: text => <span>{text || 'N/A'}</span>,
      },
      {
        title: 'Target Month',
        dataIndex: 'dueDate',
        key: 'dueDate',
        render: text => (
          <span>{text ? moment(text).format('MMM YYYY') : 'N/A'}</span>
        ),
      },
      {
        title: 'Documents',
        key: 'documents',
        render: (_, record) => {
          // Check for documents array first (new format)
          if (record.documents && record.documents.length > 0) {
            return (
              <div style={{ display: 'flex', gap: '8px' }}>
                {record.documents.map((doc, index) => {
                  // Determine icon and styling based on approval status
                  let iconColor = '#1890ff'; // Default blue
                  let overlayIcon = null;

                  if (doc.approvalStatus === 'approved') {
                    iconColor = '#52c41a'; // Green for approved
                    overlayIcon = (
                      <CheckCircleFilled
                        style={{
                          fontSize: '12px',
                          color: '#52c41a',
                          position: 'absolute',
                          top: '-4px',
                          right: '-4px',
                        }}
                      />
                    );
                  } else if (doc.approvalStatus === 'rejected') {
                    iconColor = '#f5222d'; // Red for rejected
                    overlayIcon = (
                      <CloseCircleFilled
                        style={{
                          fontSize: '12px',
                          color: '#f5222d',
                          position: 'absolute',
                          top: '-4px',
                          right: '-4px',
                        }}
                      />
                    );
                  } else {
                    // Pending status
                    overlayIcon = (
                      <ExclamationCircleFilled
                        style={{
                          fontSize: '12px',
                          color: '#faad14',
                          position: 'absolute',
                          top: '-4px',
                          right: '-4px',
                        }}
                      />
                    );
                  }

                  return (
                    <Button
                      key={doc._id || `doc-${index}`}
                      type="link"
                      onClick={() => {
                        setDocumentPreviewModal({
                          visible: true,
                          url: doc.documentLink,
                          title: `${doc.documentName ||
                            record.courseName ||
                            'Document'} Preview`,
                          documentId: doc._id,
                          assignmentId: record._id,
                          approvalStatus: doc.approvalStatus || 'pending',
                        });
                      }}
                      title={doc.documentName || `Document ${index + 1}`}
                      style={{ padding: 0 }}
                    >
                      <div
                        style={{
                          position: 'relative',
                          display: 'inline-block',
                        }}
                      >
                        <FileOutlined
                          style={{ fontSize: '18px', color: iconColor }}
                        />
                        {overlayIcon}
                      </div>
                    </Button>
                  );
                })}
              </div>
            );
          }

          // Fallback for legacy documents (old format)
          const documentUrl = record.uploadedDocument || record.documentLink;
          if (documentUrl && documentUrl !== '') {
            // Determine approval status for legacy documents
            let iconColor = '#1890ff'; // Default blue
            let overlayIcon = null;

            if (record.approvalStatus === 'approved') {
              iconColor = '#52c41a'; // Green for approved
              overlayIcon = (
                <CheckCircleFilled
                  style={{
                    fontSize: '12px',
                    color: '#52c41a',
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                  }}
                />
              );
            } else if (record.approvalStatus === 'rejected') {
              iconColor = '#f5222d'; // Red for rejected
              overlayIcon = (
                <CloseCircleFilled
                  style={{
                    fontSize: '12px',
                    color: '#f5222d',
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                  }}
                />
              );
            } else {
              // Pending status
              overlayIcon = (
                <ExclamationCircleFilled
                  style={{
                    fontSize: '12px',
                    color: '#faad14',
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                  }}
                />
              );
            }

            return (
              <Button
                type="link"
                onClick={() => {
                  // For legacy documents, use the assignment ID as the document ID since there's only one document per assignment
                  setDocumentPreviewModal({
                    visible: true,
                    url: documentUrl,
                    title: `${record.courseName || 'Document'} Preview`,
                    documentId: record._id, // Use assignment ID as document ID for legacy format
                    assignmentId: record._id,
                    approvalStatus: record.approvalStatus || 'pending',
                  });
                }}
                title="View uploaded document"
                style={{ padding: 0 }}
              >
                <div style={{ position: 'relative', display: 'inline-block' }}>
                  <FileOutlined
                    style={{ fontSize: '18px', color: iconColor }}
                  />
                  {overlayIcon}
                </div>
              </Button>
            );
          }

          return 'N/A';
        },
      },
      {
        title: 'Learning Medium',
        key: 'learningMedium',
        render: (_, record) => {
          if (record.learningMedium && record.learningMedium !== '') {
            return (
              <a
                href={record.learningMedium}
                target="_blank"
                rel="noopener noreferrer"
                className="view-course-link"
              >
                View Course
              </a>
            );
          }
          return 'N/A';
        },
      },
      {
        title: 'Mentee comments',
        key: 'menteeComments',
        render: (_, record) => {
          // For HR and Admin, just view comments
          if (
            currentUser &&
            (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)
          ) {
            return (
              <div>
                <Button
                  type="link"
                  onClick={() => handleOpenComments(record, 'mentee')}
                >
                  View comments
                </Button>
              </div>
            );
          }

          // For mentees (viewing their own assignments)
          if (
            currentUser &&
            currentUser.role !== ROLES.HR &&
            currentUser.role !== ROLES.ADMIN &&
            !urlMenteeId
          ) {
            return (
              <div>
                <Button
                  type="link"
                  onClick={() => handleOpenComments(record, 'mentee')}
                >
                  {record.menteeComments ? 'Edit comments' : 'Add comments'}
                </Button>
              </div>
            );
          }

          // For mentors viewing mentee assignments
          return (
            <div>
              <Button
                type="link"
                onClick={() => handleOpenComments(record, 'mentee')}
                disabled={!record.menteeComments}
              >
                {record.menteeComments ? 'View comments' : 'No comments yet'}
              </Button>
            </div>
          );
        },
      },
      {
        title: 'Mentor comments',
        key: 'mentorComments',
        render: (_, record) => {
          // For HR and Admin, just view comments
          if (
            currentUser &&
            (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)
          ) {
            return (
              <div>
                <Button
                  type="link"
                  onClick={() => handleOpenComments(record, 'mentor')}
                >
                  View comments
                </Button>
              </div>
            );
          }

          // For mentors (viewing mentee assignments)
          if (urlMenteeId) {
            return (
              <div>
                <Button
                  type="link"
                  onClick={() => handleOpenComments(record, 'mentor')}
                >
                  {record.mentorComments ? 'Edit comments' : 'Add comments'}
                </Button>
              </div>
            );
          }

          // For mentees viewing their own assignments
          return (
            <div>
              <Button
                type="link"
                onClick={() => handleOpenComments(record, 'mentor')}
                disabled={!record.mentorComments}
              >
                {record.mentorComments ? 'View comments' : 'No comments yet'}
              </Button>
            </div>
          );
        },
      },
      {
        title: '% of completion',
        dataIndex: 'completionPercentage',
        key: 'completionPercentage',
        render: (percentage, record) => {
          // For mentors viewing their mentees (not HR or admin)
          if (
            urlMenteeId &&
            currentUser &&
            currentUser.role !== ROLES.HR &&
            currentUser.role !== ROLES.ADMIN
          ) {
            // Determine the current status-based percentage value
            const currentValue =
              editedPercentages[record._id] !== undefined
                ? editedPercentages[record._id]
                : percentage || 0;

            return (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Input
                  type="number"
                  min={0}
                  max={100}
                  value={currentValue}
                  style={{ width: '60px' }}
                  onChange={e =>
                    handleCompletionPercentageChange(record, e.target.value)
                  }
                  onBlur={e => {
                    const newValue = e.target.value;
                    if (newValue !== String(percentage)) {
                      handleUpdateCompletionPercentage(record, newValue);
                    }
                  }}
                  onPressEnter={e => {
                    const newValue = e.target.value;
                    if (newValue !== String(percentage)) {
                      handleUpdateCompletionPercentage(record, newValue);
                    }
                  }}
                />
                <span style={{ marginLeft: '4px' }}>%</span>
              </div>
            );
          }
          // For everyone else, just display the percentage
          return `${percentage || 0}%`;
        },
      },
    ];

    // Only add Action column for HR, Admin, or when a menteeId is present in the URL
    if (
      (currentUser &&
        (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)) ||
      urlMenteeId
    ) {
      baseColumns.push({
        title: 'Action',
        key: 'action',
        render: (_, record) => (
          <div>
            <Tooltip title="Edit course">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  // TODO: Implement edit functionality
                  message.info('Edit functionality not implemented yet');
                }}
              />
            </Tooltip>
            <Tooltip title="Delete course">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          </div>
        ),
      });
    }

    // Create a copy of baseColumns to work with
    const columns = [...baseColumns];

    // Add upload document column for regular users (not HR or ADMIN)
    // This is for the 3rd case where the user is not admin/HR and not viewing a specific mentee
    if (
      currentUser &&
      currentUser.role !== ROLES.HR &&
      currentUser.role !== ROLES.ADMIN &&
      !urlMenteeId
    ) {
      columns.push({
        title: 'Upload Document',
        key: 'uploadDocument',
        render: (_, record) => {
          const handleClick = e => {
            e.preventDefault();
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = '.pdf,.jpg,.jpeg,.png';
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);

            fileInput.onchange = async event => {
              const file = event.target.files[0];
              if (!file) return;

              // Validate file type
              const allowedTypes = [
                'application/pdf',
                'image/jpeg',
                'image/jpg',
                'image/png',
              ];
              if (!allowedTypes.includes(file.type)) {
                message.error(
                  'Invalid file type. Only PDF, JPEG, JPG, and PNG files are allowed',
                );
                document.body.removeChild(fileInput);
                return;
              }

              // Validate file size (5MB limit)
              const maxSize = 5 * 1024 * 1024; // 5MB in bytes
              if (file.size > maxSize) {
                message.error('File size exceeds 5MB limit');
                document.body.removeChild(fileInput);
                return;
              }

              setUploadLoading(prev => ({ ...prev, [record._id]: true }));

              const formData = new FormData();
              formData.append('document', file);
              formData.append('assignmentId', record._id);

              try {
                // Using XMLHttpRequest instead of the request utility to properly handle file uploads
                const xhr = new XMLHttpRequest();
                xhr.open('POST', API_ENDPOINTS.UPLOAD_COURSE_DOCUMENT, true);
                xhr.setRequestHeader(
                  'Authorization',
                  localStorage.getItem('token'),
                );

                // Create a promise to handle the async XHR request
                const uploadPromise = new Promise((resolve, reject) => {
                  xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                      try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                      } catch (parseError) {
                        reject(new Error('Invalid response format'));
                      }
                    } else {
                      reject(
                        new Error(`Upload failed with status ${xhr.status}`),
                      );
                    }
                  };

                  xhr.onerror = function() {
                    reject(new Error('Network error during upload'));
                  };
                });

                // Send the form data
                xhr.send(formData);

                // Wait for the upload to complete
                const response = await uploadPromise;

                setUploadLoading(prev => ({ ...prev, [record._id]: false }));

                if (response && response.status === 200) {
                  message.success(`${file.name} uploaded successfully`);

                  // Save current state before fetching new data
                  const currentData = [...filteredAssignments];

                  // Fetch new data without losing current view
                  try {
                    await fetchAllAssignments();
                  } catch (err) {
                    // Error refreshing data
                    // Restore previous state if fetch fails
                    setFilteredAssignments(currentData);
                  }
                } else {
                  message.error(
                    `${file.name} upload failed: ${response?.message ||
                      'Unknown error'}`,
                  );
                }
              } catch (error) {
                setUploadLoading(prev => ({ ...prev, [record._id]: false }));
                message.error(
                  `${file.name} upload failed: ${error.message ||
                    'Unknown error'}`,
                );
              } finally {
                document.body.removeChild(fileInput);
              }
            };

            fileInput.click();
          };

          return (
            <Button
              icon={<UploadOutlined />}
              loading={uploadLoading[record._id]}
              onClick={handleClick}
            >
              {record.uploadedDocument ? 'Update' : 'Upload'}
            </Button>
          );
        },
      });
    }

    // If user is HR or ADMIN, add the employee and mentor columns at the beginning
    if (
      !currentUser ||
      currentUser.role === ROLES.HR ||
      currentUser.role === ROLES.ADMIN
    ) {
      return [
        {
          title: 'Employee Name',
          dataIndex: 'menteeName',
          key: 'menteeName',
          render: text => <span>{text || 'N/A'}</span>,
        },
        {
          title: 'Employee ID',
          dataIndex: 'employeeId',
          key: 'employeeId',
          render: text => <span>{text || 'N/A'}</span>,
        },
        {
          title: 'Mentors',
          dataIndex: 'mentorName',
          key: 'mentorName',
          render: text => <span>{text || 'N/A'}</span>,
        },
        ...columns,
      ];
    }

    // For other roles, just return the columns (which may include the upload column)
    return columns;
  };

  const columns = getColumns();

  // Handle document approval or rejection
  const handleDocumentApproval = async status => {
    try {
      setApprovalLoading(true);
      const { documentId, assignmentId } = documentPreviewModal;

      // Validate that both document ID and assignment ID are present
      if (!documentId || !assignmentId) {
        message.error(
          'Document ID and Assignment ID are required for approval',
        );
        setApprovalLoading(false);
        return;
      }

      const response = await request(API_ENDPOINTS.DOCUMENT_APPROVAL, {
        method: 'POST',
        body: JSON.stringify({
          assignmentId,
          documentId,
          status,
          comment: '',
        }),
      });

      if (response && response.status === 1) {
        message.success(`Document ${status} successfully`);

        // Update local state
        setDocumentPreviewModal(prev => ({
          ...prev,
          approvalStatus: status,
        }));

        // Refresh the assignments data
        await fetchAllAssignments();
      } else {
        message.error(`Failed to ${status} document`);
      }
    } catch (error) {
      message.error(
        `Error: ${error.message || 'Failed to process document approval'}`,
      );
    } finally {
      setApprovalLoading(false);
    }
  };

  const handleCloseDocumentPreview = () => {
    setDocumentPreviewModal({
      visible: false,
      url: '',
      title: '',
      documentId: '',
      assignmentId: '',
      approvalStatus: 'pending',
    });
  };

  // Open comments modal for viewing/editing comments
  const handleOpenComments = (record, commentType) => {
    setCommentsModal({
      visible: true,
      assignmentId: record._id,
      mentorComments: record.mentorComments || '',
      menteeComments: record.menteeComments || '',
      commentType,
      title: `${record.courseName} - ${
        commentType === 'mentor' ? 'Mentor' : 'Mentee'
      } Comments`,
    });
  };

  // Close comments modal
  const handleCloseComments = () => {
    setCommentsModal({
      visible: false,
      assignmentId: '',
      mentorComments: '',
      menteeComments: '',
      commentType: 'mentor',
      title: '',
    });
  };

  // Update comments in state while typing
  const handleCommentChange = e => {
    const { value } = e.target;
    setCommentsModal(prev => ({
      ...prev,
      [prev.commentType === 'mentor'
        ? 'mentorComments'
        : 'menteeComments']: value,
    }));
  };

  // State to track edited completion percentages before saving
  const [editedPercentages, setEditedPercentages] = useState({});

  // Handle input change for completion percentage
  const handleCompletionPercentageChange = (record, value) => {
    setEditedPercentages(prev => ({
      ...prev,
      [record._id]: value,
    }));
  };

  // Handle updating completion percentage
  const handleUpdateCompletionPercentage = async (record, newPercentage) => {
    // Prevent duplicate API calls
    if (isUpdatingPercentage) return;

    try {
      setIsUpdatingPercentage(true);
      setLoading(true);

      // Validate the percentage is between 0 and 100
      const percentage = Math.min(
        Math.max(parseInt(newPercentage, 10) || 0, 0),
        100,
      );

      // Determine completionStatus based on percentage
      let completionStatus = 'Assigned';
      if (percentage === 100) {
        completionStatus = 'Completed';
      }

      // Call API to update course status using the existing endpoint
      const response = await request(
        `${API_ENDPOINTS.TECH_ROADMAP_STATUS}/${record._id}`,
        {
          method: 'PATCH',
          body: JSON.stringify({
            completionStatus,
            completionPercentage: percentage,
          }),
        },
      );

      // Check for successful response (backend might return status 200 or 1)
      if (response && (response.status === 1 || response.status === 200)) {
        // Use custom message component without the error icon
        const customMessage = (
          <div className="custom-success-message">
            <span style={{ color: '#52c41a', marginRight: '8px' }}>✓</span>
            <span>Completion percentage updated successfully</span>
          </div>
        );

        message.open({
          content: customMessage,
          className: 'custom-message-success',
          duration: 3,
        });

        // Clear the edited value from local state
        setEditedPercentages(prev => {
          const newState = { ...prev };
          delete newState[record._id];
          return newState;
        });

        // Refresh the assignments data
        await fetchAllAssignments();
      } else {
        message.error(
          response?.message || 'Failed to update completion percentage',
        );
      }
    } catch (error) {
      message.error(
        `Error: ${error.message || 'Failed to update completion percentage'}`,
      );
    } finally {
      setLoading(false);
      // Reset the updating flag after a short delay to prevent duplicate calls
      setTimeout(() => {
        setIsUpdatingPercentage(false);
      }, 300);
    }
  };

  // Save comments to backend
  const handleSaveComments = async () => {
    try {
      setCommentLoading(true);
      const {
        assignmentId,
        commentType,
        mentorComments,
        menteeComments,
      } = commentsModal;

      const comment =
        commentType === 'mentor' ? mentorComments : menteeComments;

      const response = await request(API_ENDPOINTS.UPDATE_COMMENTS, {
        method: 'POST',
        body: JSON.stringify({
          assignmentId,
          commentType,
          comment,
        }),
      });

      if (response && response.status === 1) {
        message.success(
          `${commentType.charAt(0).toUpperCase() +
            commentType.slice(1)} comments saved successfully`,
        );

        // Refresh the assignments data
        await fetchAllAssignments();

        // Close the modal
        handleCloseComments();
      } else {
        message.error('Failed to save comments');
      }
    } catch (error) {
      message.error(`Error: ${error.message || 'Failed to save comments'}`);
    } finally {
      setCommentLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Helmet>
        <title>Course Assignment</title>
        <meta name="description" content="Course Assignment" />
      </Helmet>
      <Card
        title={
          currentUser &&
          (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)
            ? 'Tech Roadmap & Certifications - Admin'
            : 'Tech Roadmap & Certifications'
        }
        extra={
          <>
            {isSuperAdminUser ? (
              <Button
                type="primary"
                onClick={() => setIsCourseModalVisible(true)}
                style={{
                  backgroundColor: '#4d186e',
                  borderColor: '#4d186e',
                }}
              >
                Add a new course
              </Button>
            ) : (
              // Only show 'Assign a new course' button for HR, ADMIN, or when menteeId is in URL
              user &&
              (user.role === ROLES.HR ||
                user.role === ROLES.ADMIN ||
                urlMenteeId) && (
                <Button
                  type="primary"
                  onClick={() => setMenteeRoadmapFormModalVisible(true)}
                  style={{
                    backgroundColor: '#4d186e',
                    borderColor: '#4d186e',
                  }}
                >
                  Assign a new course
                </Button>
              )
            )}
          </>
        }
        style={{
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          border: '1px solid #e8e8e8',
        }}
        headStyle={{
          backgroundColor: '#f6f6f6',
          borderBottom: '1px solid #e8e8e8',
          padding: '16px 24px',
          fontSize: '18px',
          fontWeight: '500',
          color: '#4d186e',
        }}
      >
        <div style={{ marginBottom: 16 }}>
          <Input
            placeholder="Search by employee name, course name, or mentor"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 300 }}
          />
        </div>
        <Table
          dataSource={getSearchFilteredAssignments()}
          columns={columns}
          rowKey="_id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          locale={{ emptyText: 'No courses assigned yet' }}
        />
      </Card>

      {/* Modal for adding a new course */}
      <TechRoadmap
        visible={isCourseModalVisible}
        onCancel={handleCourseModalCancel}
        onSuccess={handleCourseModalSuccess}
      />

      {/* Document Preview Modal */}
      <Modal
        title={documentPreviewModal.title}
        open={documentPreviewModal.visible}
        onCancel={handleCloseDocumentPreview}
        footer={
          // Only show approval buttons for mentors viewing their mentees' documents
          user &&
          urlMenteeId &&
          user.role !== ROLES.HR &&
          user.role !== ROLES.ADMIN ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                gap: '10px',
              }}
            >
              {documentPreviewModal.approvalStatus === 'pending' && (
                <>
                  <Button
                    type="primary"
                    onClick={() => handleDocumentApproval('approved')}
                    loading={approvalLoading}
                    style={{
                      backgroundColor: '#52c41a',
                      borderColor: '#52c41a',
                    }}
                  >
                    Approve Document
                  </Button>
                  <Button
                    type="primary"
                    danger
                    onClick={() => handleDocumentApproval('rejected')}
                    loading={approvalLoading}
                  >
                    Reject Document
                  </Button>
                </>
              )}
              {documentPreviewModal.approvalStatus === 'approved' && (
                <div style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  ✓ Document Approved
                </div>
              )}
              {documentPreviewModal.approvalStatus === 'rejected' && (
                <div style={{ color: '#f5222d', fontWeight: 'bold' }}>
                  ✗ Document Rejected
                </div>
              )}
              <Button onClick={handleCloseDocumentPreview}>Close</Button>
            </div>
          ) : null
        }
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ height: '80vh', padding: 0 }}
      >
        <iframe
          src={documentPreviewModal.url}
          title="Document Preview"
          width="100%"
          height="100%"
          style={{ border: 'none' }}
        />
      </Modal>

      {/* Modal for MenteeRoadmapForm */}
      <Modal
        title="Assign a New Course"
        open={menteeRoadmapFormModalVisible}
        onCancel={handleMenteeRoadmapFormModalCancel}
        footer={null}
        width={800}
      >
        <MenteeRoadmapForm
          menteeId={urlMenteeId || (selectedMentee && selectedMentee._id)}
          mentorId={currentUser?._id}
          menteeRole={employeeData?.designation || selectedMentee?.designation}
          menteeName={employeeData?.name || selectedMentee?.name}
          onSuccess={() => {
            setMenteeRoadmapFormModalVisible(false);
            fetchAllAssignments();
          }}
          onCancel={handleMenteeRoadmapFormModalCancel}
        />
      </Modal>

      {/* Comments Modal */}
      <Modal
        title={commentsModal.title}
        open={commentsModal.visible}
        onCancel={handleCloseComments}
        footer={[
          <Button key="cancel" onClick={handleCloseComments}>
            {currentUser &&
            (currentUser.role === ROLES.HR || currentUser.role === ROLES.ADMIN)
              ? 'Close'
              : 'Cancel'}
          </Button>,
          // Only show save button if user can edit these comments (not HR or admin)
          ((commentsModal.commentType === 'mentor' &&
            urlMenteeId &&
            !(
              currentUser &&
              (currentUser.role === ROLES.HR ||
                currentUser.role === ROLES.ADMIN)
            )) ||
            (commentsModal.commentType === 'mentee' &&
              !urlMenteeId &&
              currentUser &&
              currentUser.role !== ROLES.HR &&
              currentUser.role !== ROLES.ADMIN)) && (
            <Button
              key="save"
              type="primary"
              onClick={handleSaveComments}
              loading={commentLoading}
            >
              Save Comments
            </Button>
          ),
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          {/* Show mentor comments */}
          <div style={{ marginBottom: 16 }}>
            <h4>Mentor Comments:</h4>
            {commentsModal.commentType === 'mentor' &&
            urlMenteeId &&
            !(
              currentUser &&
              (currentUser.role === ROLES.HR ||
                currentUser.role === ROLES.ADMIN)
            ) ? (
              <Input.TextArea
                rows={4}
                value={commentsModal.mentorComments}
                onChange={handleCommentChange}
                placeholder="Enter mentor comments here..."
              />
            ) : (
              <div
                style={{
                  padding: '8px 12px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '2px',
                  minHeight: '80px',
                  backgroundColor: '#f5f5f5',
                }}
              >
                {commentsModal.mentorComments || 'No mentor comments yet.'}
              </div>
            )}
          </div>

          {/* Show mentee comments */}
          <div>
            <h4>Mentee Comments:</h4>
            {commentsModal.commentType === 'mentee' &&
            (!urlMenteeId &&
              currentUser &&
              currentUser.role !== ROLES.HR &&
              currentUser.role !== ROLES.ADMIN) ? (
              <Input.TextArea
                rows={4}
                value={commentsModal.menteeComments}
                onChange={handleCommentChange}
                placeholder="Enter mentee comments here..."
              />
            ) : (
              <div
                style={{
                  padding: '8px 12px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '2px',
                  minHeight: '80px',
                  backgroundColor: '#f5f5f5',
                }}
              >
                {commentsModal.menteeComments || 'No mentee comments yet.'}
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CourseAssignment;
