/**
 * Tech Roadmap Assign Course Service
 */
const TechRoadmap = require('../../models/techRoadmap.model');
const User = require('../../models/user.model');
const GeneralError = require('../../util/GeneralError');
const fs = require('fs');
const path = require('path');

/**
 * Assign a course to a mentee
 * @param {Object} data - Course assignment data
 * @returns {Promise} - Promise object representing the created assignment
 */
const assignCourse = async (data) => {
  try {
    // Validate mentee and mentor exist
    const mentee = await User.findById(data.menteeId).select('_id');
    if (!mentee) {
      throw new GeneralError('Mentee not found', 404);
    }

    const mentor = await User.findById(data.mentorId).select('_id');
    if (!mentor) {
      throw new GeneralError('Mentor not found', 404);
    }

    // Create new tech roadmap assignment
    const assignment = new TechRoadmap(data);
    await assignment.save();

    return {
      status: 200,
      message: 'Course assigned successfully',
      data: assignment,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error assigning course', error.statusCode || 400);
  }
};

/**
 * Get all course assignments for a mentee
 * @param {String} menteeId - Mentee ID
 * @returns {Promise} - Promise object representing the list of assignments
 */
const getMenteeCourses = async (menteeId) => {
  try {
    // Validate mentee exists
    const mentee = await User.findById(menteeId).select('_id');
    if (!mentee) {
      throw new GeneralError('Mentee not found', 404);
    }

    // Get all assignments for the mentee
    const assignments = await TechRoadmap.find({ menteeId }).sort({ createdAt: -1 });

    return {
      status: 200,
      data: assignments,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error retrieving courses', error.statusCode || 400);
  }
};

/**
 * Update course completion status
 * @param {String} assignmentId - Assignment ID
 * @param {Object} data - Update data (completionStatus, rating, etc.)
 * @returns {Promise} - Promise object representing the updated assignment
 */
const updateCourseStatus = async (assignmentId, data) => {
  try {
    // Find and update the assignment
    const assignment = await TechRoadmap.findByIdAndUpdate(
      assignmentId,
      { ...data, updatedAt: Date.now() },
      { new: true }
    );

    if (!assignment) {
      throw new GeneralError('Assignment not found', 404);
    }

    return {
      status: 200,
      message: 'Course status updated successfully',
      data: assignment,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error updating course status', error.statusCode || 400);
  }
};

/**
 * Get all tech roadmap assignments with populated user details
 * @returns {Promise} - Promise object representing all assignments with user details
 */
const getAllAssignments = async () => {
  try {
    // Get all assignments from the techroadmaps collection
    const assignments = await TechRoadmap.find().sort({ createdAt: -1 });
    console.log(`Found ${assignments.length} assignments`);
    
    // Create an array to store enriched assignments
    const enrichedAssignments = [];
    
    // Since we don't have actual users in the database, let's extract the user IDs
    // and use them to create meaningful placeholder names
    const menteeIds = new Set();
    const mentorIds = new Set();
    
    // Collect all unique mentee and mentor IDs
    assignments.forEach(assignment => {
      if (assignment.menteeId) menteeIds.add(assignment.menteeId.toString());
      if (assignment.mentorId) mentorIds.add(assignment.mentorId.toString());
    });
    
    console.log(`Found ${menteeIds.size} unique mentees and ${mentorIds.size} unique mentors`);
    
    // Create maps to store mentee and mentor names
    const menteeNames = {};
    const mentorNames = {};
    
    // Try to fetch user details from the database first
    try {
      const users = await User.find({
        $or: [
          { _id: { $in: Array.from(menteeIds) } },
          { _id: { $in: Array.from(mentorIds) } }
        ]
      }).select('_id firstName lastName employeeId mentorId');
      
      console.log(`Found ${users.length} users in the database`);
      
      // Create a map of user IDs to user objects for quick lookup
      const userMap = {};
      users.forEach(user => {
        userMap[user._id.toString()] = user;
      });
      
      // Populate the maps with actual user data if available
      users.forEach(user => {
        const userId = user._id.toString();
        // Combine firstName and lastName to create the full name
        const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
        
        if (menteeIds.has(userId)) {
          menteeNames[userId] = {
            name: fullName || `Mentee ${userId.substring(userId.length - 5)}`,
            employeeId: user.employeeId || '65'
          };
          
          // If this user has a mentorId, add it to the mentorIds set
          if (user.mentorId && !mentorIds.has(user.mentorId.toString())) {
            mentorIds.add(user.mentorId.toString());
          }
        }
        
        if (mentorIds.has(userId)) {
          mentorNames[userId] = { 
            name: fullName || `Mentor ${userId.substring(userId.length - 5)}` 
          };
        }
      });
      
      // Do a second pass to fetch any mentors that weren't in the original query
      const missingMentorIds = Array.from(mentorIds).filter(id => !mentorNames[id]);
      if (missingMentorIds.length > 0) {
        console.log(`Fetching ${missingMentorIds.length} additional mentors`);
        const additionalMentors = await User.find({
          _id: { $in: missingMentorIds }
        }).select('_id firstName lastName');
        
        additionalMentors.forEach(mentor => {
          const mentorId = mentor._id.toString();
          const fullName = `${mentor.firstName || ''} ${mentor.lastName || ''}`.trim();
          mentorNames[mentorId] = { 
            name: fullName || `Mentor ${mentorId.substring(mentorId.length - 5)}` 
          };
        });
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
    
    // For any mentee or mentor without a corresponding user, create a placeholder
    menteeIds.forEach(menteeId => {
      if (!menteeNames[menteeId]) {
        // Create a placeholder name based on the ID
        const shortId = menteeId.substring(menteeId.length - 5);
        menteeNames[menteeId] = {
          name: `Mentee ${shortId}`,
          employeeId: '65'
        };
      }
    });
    
    mentorIds.forEach(mentorId => {
      if (!mentorNames[mentorId]) {
        // Create a placeholder name based on the ID
        const shortId = mentorId.substring(mentorId.length - 5);
        mentorNames[mentorId] = { name: `Mentor ${shortId}` };
      }
    });
    
    // Process each assignment to add user details
    for (const assignment of assignments) {
      const assignmentObj = assignment.toObject();
      
      // Add mentee details
      if (assignmentObj.menteeId) {
        const menteeId = assignmentObj.menteeId.toString();
        const menteeDetails = menteeNames[menteeId] || { name: 'Unknown Mentee', employeeId: '65' };
        assignmentObj.menteeName = menteeDetails.name;
        assignmentObj.employeeId = menteeDetails.employeeId;
      } else {
        assignmentObj.menteeName = 'Unknown Mentee';
        assignmentObj.employeeId = '65';
      }
      
      // Add mentor details
      if (assignmentObj.mentorId) {
        const mentorId = assignmentObj.mentorId.toString();
        const mentorDetails = mentorNames[mentorId] || { name: 'Unknown Mentor' };
        assignmentObj.mentorName = mentorDetails.name;
      } else {
        assignmentObj.mentorName = 'Unknown Mentor';
      }
      
      enrichedAssignments.push(assignmentObj);
    }

    return {
      status: 200,
      data: enrichedAssignments,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error retrieving all assignments', error.statusCode || 400);
  }
};

/**
 * Upload document for a course assignment
 * @param {String} assignmentId - Assignment ID
 * @param {Object} file - Uploaded file object
 * @returns {Promise} - Promise object representing the updated assignment
 */
const uploadDocument = async (assignmentId, file) => {
  try {
    // Find the assignment
    const assignment = await TechRoadmap.findById(assignmentId);
    if (!assignment) {
      throw new GeneralError('Assignment not found', 404);
    }

    // Generate a unique filename
    const fileExtension = path.extname(file.originalname);
    const fileName = `tech-roadmap/${assignmentId}-${Date.now()}${fileExtension}`;

    // Use the specific bucket name
    const bucketName = 'demodevmedia-timesheet.growexx.com';
    const bucketPath = 'demo';
    
    // Upload file to S3
    const data = {
      Key: `${bucketPath}/${fileName}`,
      Bucket: bucketName,
      Body: file.buffer,
      ContentType: file.mimetype
      // Removed ACL: 'public-read' as the bucket does not allow ACLs
    };
    
    // Use AWS SDK directly for better error handling
    const AWS = require('aws-sdk');
    const s3 = new AWS.S3({
      accessKeyId: process.env.ACCESS_KEY_ID,
      secretAccessKey: process.env.SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-2'
    });
    
    try {
      console.log('Attempting to upload to S3 with params:', {
        bucket: bucketName,
        key: data.Key,
        contentType: file.mimetype
      });
      
      const uploadResult = await s3.upload(data).promise();
      console.log('S3 upload successful:', uploadResult);
      
      // Generate a pre-signed URL that will be valid for 7 days
      const signedUrlExpireSeconds = 60 * 60 * 24 * 7; // 7 days
      const preSignedUrl = s3.getSignedUrl('getObject', {
        Bucket: bucketName,
        Key: uploadResult.Key,
        Expires: signedUrlExpireSeconds
      });
      
      console.log('Generated pre-signed URL:', preSignedUrl);
      
      // Create a new document object
      const newDocument = {
        documentLink: preSignedUrl,
        documentKey: uploadResult.Key,
        documentName: file.originalname,
        uploadedAt: new Date()
      };
      
      // Initialize documents array if it doesn't exist
      if (!assignment.documents) {
        assignment.documents = [];
      }
      
      // Add the new document to the documents array
      assignment.documents.push(newDocument);
      
      console.log('Adding new document to assignment:', {
        documentName: newDocument.documentName,
        documentKey: newDocument.documentKey,
        totalDocuments: assignment.documents.length
      });
      
      await assignment.save();
      
      return {
        status: 200,
        message: 'Document uploaded successfully to S3',
        data: assignment,
      };
    } catch (s3Error) {
      console.error('S3 Upload Error:', s3Error);
      throw new GeneralError(`Failed to upload document to S3: ${s3Error.message}`, 500);
    }
  } catch (error) {
    throw new GeneralError(error.message || 'Error uploading document', error.statusCode || 400);
  }
};

const deleteCourseAssignment = async (assignmentId) => {
  try {
    const assignment = await TechRoadmap.findByIdAndDelete(assignmentId);
    if (!assignment) {
      throw new GeneralError("Course assignment not found", 404);
    }
 
    return {
      status: 200,
      message: "Course assignment deleted successfully",
    };
  } catch (error) {
    throw new GeneralError(
      error.message || "Error deleting course assignment",
      error.statusCode || 400
    );
  }
};

module.exports = {
  assignCourse,
  getMenteeCourses,
  updateCourseStatus,
  getAllAssignments,
  uploadDocument,
  deleteCourseAssignment
};
