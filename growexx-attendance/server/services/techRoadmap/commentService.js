/**
 * Comment Service
 * Handles updating mentor and mentee comments for course assignments
 */

const TechRoadmap = require('../../models/techRoadmap.model');
const GeneralError = require('../../util/GeneralError');

/**
 * Update comments for a course assignment
 * @param {string} assignmentId - The ID of the assignment
 * @param {string} commentType - The type of comment ('mentor' or 'mentee')
 * @param {string} comment - The comment text
 * @returns {Object} Updated assignment with comments
 */
const updateComments = async (assignmentId, commentType, comment) => {
  try {
    if (!assignmentId || !commentType || comment === undefined) {
      throw new GeneralError('Missing required parameters', 400);
    }

    if (commentType !== 'mentor' && commentType !== 'mentee') {
      throw new GeneralError('Invalid comment type. Type must be either "mentor" or "mentee"', 400);
    }

    // Find the assignment
    const assignment = await TechRoadmap.findById(assignmentId);
    if (!assignment) {
      throw new GeneralError('Assignment not found', 404);
    }

    // Update the appropriate comment field
    if (commentType === 'mentor') {
      assignment.mentorComments = comment;
    } else {
      assignment.menteeComments = comment;
    }

    // Save the updated assignment
    await assignment.save();

    // Return the updated assignment
    return assignment;
  } catch (error) {
    // Re-throw the error with a consistent format
    if (error instanceof GeneralError) {
      throw error;
    }
    throw new GeneralError(`Error updating comments: ${error.message}`, 500);
  }
};

module.exports = {
  updateComments,
};
