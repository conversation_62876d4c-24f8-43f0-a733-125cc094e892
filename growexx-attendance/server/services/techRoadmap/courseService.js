/**
 * Tech Roadmap Course Service
 */
const SuperadminCourse = require('../../models/superadminCourse');
const GeneralError = require('../../util/GeneralError');

/**
 * Add a new course
 * @param {Object} data - Course data
 * @returns {Promise} - Promise object representing the created course
 */
const addCourse = async (data) => {
  try {
    const course = new SuperadminCourse(data);
    await course.save();
    return {
      status: 200,
      message: 'Course added successfully',
      data: course,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error adding course', error.statusCode || 400);
  }
};

/**
 * Get all courses
 * @returns {Promise} - Promise object representing the list of courses
 */
const getAllCourses = async () => {
  try {
    const courses = await SuperadminCourse.find().sort({ createdAt: -1 });
    return {
      status: 200,
      data: courses,
    };
  } catch (error) {
    throw new GeneralError(error.message || 'Error retrieving courses', error.statusCode || 400);
  }
};

module.exports = {
  addCourse,
  getAllCourses,
};
