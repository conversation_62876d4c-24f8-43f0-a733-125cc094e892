/**
 * Document Approval Service
 * Handles approval and rejection of documents uploaded by mentees
 */

const TechRoadmap = require('../../models/techRoadmap.model');
const GeneralError = require('../../util/GeneralError');

/**
 * Update document approval status
 * @param {string} assignmentId - The ID of the assignment
 * @param {string} documentId - The ID of the document within the assignment's documents array
 * @param {string} status - The approval status ('approved' or 'rejected')
 * @param {string} userId - The ID of the mentor approving/rejecting the document
 * @param {string} comment - Optional comment for the approval/rejection
 * @returns {Object} Updated assignment with document status
 */
const updateDocumentApprovalStatus = async (assignmentId, documentId, status, userId, comment = '') => {
  try {
    if (!assignmentId || !documentId || !status || !userId) {
      throw new GeneralError('Missing required parameters', 400);
    }

    if (status !== 'approved' && status !== 'rejected') {
      throw new GeneralError('Invalid status. Status must be either "approved" or "rejected"', 400);
    }

    // Find the assignment
    const assignment = await TechRoadmap.findById(assignmentId);
    if (!assignment) {
      throw new GeneralError('Assignment not found', 404);
    }

    // Find the document in the documents array
    const documentIndex = assignment.documents.findIndex(
      doc => doc._id.toString() === documentId
    );

    if (documentIndex === -1) {
      throw new GeneralError('Document not found in the assignment', 404);
    }

    // Update the document's approval status
    assignment.documents[documentIndex].approvalStatus = status;
    assignment.documents[documentIndex].approvalDate = new Date();
    assignment.documents[documentIndex].approvalComment = comment;
    
    // Only set approvedBy if it's a valid MongoDB ObjectId (not 'system')
    if (userId !== 'system') {
      assignment.documents[documentIndex].approvedBy = userId;
    }

    // Save the updated assignment
    await assignment.save();

    // Return the updated assignment
    return assignment;
  } catch (error) {
    // Re-throw the error with a consistent format
    if (error instanceof GeneralError) {
      throw error;
    }
    throw new GeneralError(`Error updating document approval status: ${error.message}`, 500);
  }
};

module.exports = {
  updateDocumentApprovalStatus,
};
