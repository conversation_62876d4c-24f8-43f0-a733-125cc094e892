/**
 * Update Assignment Service
 * Handles updating assignment details like completion percentage
 */

const TechRoadmap = require('../../models/techRoadmap.model');
const GeneralError = require('../../util/GeneralError');

/**
 * Update assignment details
 * @param {string} assignmentId - The ID of the assignment
 * @param {Object} updateData - Data to update (e.g., completionPercentage)
 * @returns {Object} Updated assignment
 */
const updateAssignment = async (assignmentId, updateData) => {
  try {
    if (!assignmentId) {
      throw new GeneralError('Missing assignment ID', 400);
    }

    // Find the assignment
    const assignment = await TechRoadmap.findById(assignmentId);
    if (!assignment) {
      throw new GeneralError('Assignment not found', 404);
    }

    // Update completion percentage if provided
    if (updateData.completionPercentage !== undefined) {
      // Validate percentage is between 0 and 100
      const percentage = parseInt(updateData.completionPercentage, 10);
      if (isNaN(percentage) || percentage < 0 || percentage > 100) {
        throw new GeneralError('Completion percentage must be between 0 and 100', 400);
      }
      assignment.completionPercentage = percentage;
    }

    // Save the updated assignment
    await assignment.save();

    return assignment;
  } catch (error) {
    if (error instanceof GeneralError) {
      throw error;
    }
    throw new GeneralError(`Error updating assignment: ${error.message}`, 500);
  }
};

module.exports = {
  updateAssignment,
};
